// 项目列表页面
const { projectApi } = require('../../utils/api.js');
const { formatTime, getStatusText, calculateProgress } = require('../../utils/util.js');
const app = getApp();

Page({
  data: {
    projects: [],
    loading: true,
    refreshing: false,
    searchKeyword: '',
    filterStatus: 'ALL',
    statusOptions: [
      { value: 'ALL', label: '全部' },
      { value: 'PLANNING', label: '计划中' },
      { value: 'ACTIVE', label: '进行中' },
      { value: 'COMPLETED', label: '已完成' },
      { value: 'ARCHIVED', label: '已归档' }
    ],
    showFilter: false,
    page: 1,
    hasMore: true
  },

  onLoad() {
    this.checkLogin();
  },

  onShow() {
    if (app.isLoggedIn()) {
      this.loadProjects();
    }
  },

  // 检查登录状态
  checkLogin() {
    if (!app.isLoggedIn()) {
      wx.reLaunch({
        url: '/pages/login/login'
      });
      return;
    }
    this.loadProjects();
  },

  // 加载项目列表
  async loadProjects(refresh = false) {
    if (refresh) {
      this.setData({
        page: 1,
        hasMore: true,
        projects: []
      });
    }

    this.setData({ loading: refresh ? false : true, refreshing: refresh });

    try {
      const params = {
        page: this.data.page,
        limit: 10,
        search: this.data.searchKeyword,
        status: this.data.filterStatus === 'ALL' ? '' : this.data.filterStatus
      };

      const response = await projectApi.getProjectList(params);
      const newProjects = response.data || [];

      this.setData({
        projects: refresh ? newProjects : [...this.data.projects, ...newProjects],
        hasMore: newProjects.length >= 10,
        page: refresh ? 2 : this.data.page + 1,
        loading: false,
        refreshing: false
      });

    } catch (error) {
      console.log('API服务器未连接，使用模拟数据');
      // 使用模拟数据
      const mockProjects = this.getMockProjects();
      this.setData({
        projects: refresh ? mockProjects : [...this.data.projects, ...mockProjects],
        hasMore: false,
        loading: false,
        refreshing: false
      });
    }
  },

  // 获取模拟项目数据
  getMockProjects() {
    return [
      {
        id: 1,
        title: 'LabSync 实验室管理系统',
        description: '一个现代化的实验室管理系统，支持项目管理、任务分配、团队协作等功能',
        status: 'ACTIVE',
        startDate: '2024-01-15',
        members: [
          { id: 1, name: '张三', avatar: '' },
          { id: 2, name: '李四', avatar: '' },
          { id: 3, name: '王五', avatar: '' }
        ],
        tasks: [
          { id: 1, status: 'COMPLETED' },
          { id: 2, status: 'COMPLETED' },
          { id: 3, status: 'IN_PROGRESS' },
          { id: 4, status: 'TODO' }
        ]
      },
      {
        id: 2,
        title: '数据分析平台',
        description: '基于机器学习的数据分析和可视化平台，帮助研究人员更好地理解实验数据',
        status: 'PLANNING',
        startDate: '2024-02-01',
        members: [
          { id: 4, name: '赵六', avatar: '' },
          { id: 5, name: '钱七', avatar: '' }
        ],
        tasks: [
          { id: 5, status: 'TODO' },
          { id: 6, status: 'TODO' }
        ]
      },
      {
        id: 3,
        title: '智能设备监控系统',
        description: '实时监控实验室设备状态，预警异常情况，提高设备使用效率',
        status: 'COMPLETED',
        startDate: '2023-11-01',
        members: [
          { id: 6, name: '孙八', avatar: '' }
        ],
        tasks: [
          { id: 7, status: 'COMPLETED' },
          { id: 8, status: 'COMPLETED' },
          { id: 9, status: 'COMPLETED' }
        ]
      }
    ];
  },

  // 搜索输入
  onSearchInput(e) {
    this.setData({
      searchKeyword: e.detail.value
    });
  },

  // 执行搜索
  handleSearch() {
    this.loadProjects(true);
  },

  // 清除搜索
  clearSearch() {
    this.setData({
      searchKeyword: ''
    });
    this.loadProjects(true);
  },

  // 切换筛选器显示
  toggleFilter() {
    this.setData({
      showFilter: !this.data.showFilter
    });
  },

  // 选择状态筛选
  selectStatus(e) {
    const status = e.currentTarget.dataset.status;
    this.setData({
      filterStatus: status,
      showFilter: false
    });
    this.loadProjects(true);
  },

  // 下拉刷新
  onPullDownRefresh() {
    this.loadProjects(true);
    wx.stopPullDownRefresh();
  },

  // 上拉加载更多
  onReachBottom() {
    if (this.data.hasMore && !this.data.loading) {
      this.loadProjects();
    }
  },

  // 跳转到项目详情
  goToProjectDetail(e) {
    const projectId = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/pages/project-detail/project-detail?id=${projectId}`
    });
  },

  // 创建新项目
  createProject() {
    wx.showModal({
      title: '创建项目',
      content: '此功能需要在详情页面实现',
      showCancel: false
    });
  },

  // 格式化项目状态
  formatStatus(status) {
    return getStatusText(status, 'project');
  },

  // 格式化时间
  formatTime(time) {
    return formatTime(time, 'YYYY-MM-DD');
  },

  // 计算项目进度
  calculateProgress(tasks) {
    return calculateProgress(tasks);
  },

  // 获取项目状态样式类
  getStatusClass(status) {
    const classMap = {
      'PLANNING': 'status-pending',
      'ACTIVE': 'status-active',
      'COMPLETED': 'status-completed',
      'ARCHIVED': 'status-cancelled'
    };
    return classMap[status] || 'status-pending';
  },

  // 计算项目进度颜色
  getProgressColor(progress) {
    if (progress < 30) return '#EF4444';
    if (progress < 70) return '#F59E0B';
    return '#10B981';
  }
});