/* 首页样式 */

.container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

/* 用户信息卡片 */
.user-card {
  margin-bottom: 30rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.user-info {
  display: flex;
  align-items: center;
  position: relative;
}

.user-details {
  flex: 1;
  margin-left: 20rpx;
}

.user-name {
  font-size: 36rpx;
  font-weight: 600;
  margin-bottom: 8rpx;
}

.user-role {
  font-size: 26rpx;
  opacity: 0.9;
  margin-bottom: 4rpx;
}

.user-department {
  font-size: 24rpx;
  opacity: 0.8;
}

.user-actions {
  position: relative;
}

.notification-icon {
  font-size: 48rpx;
  cursor: pointer;
}

.badge {
  position: absolute;
  top: -8rpx;
  right: -8rpx;
  background-color: #ef4444;
  color: white;
  font-size: 20rpx;
  padding: 4rpx 8rpx;
  border-radius: 20rpx;
  min-width: 32rpx;
  text-align: center;
}

/* 统计卡片网格 */
.stats-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20rpx;
  margin-bottom: 30rpx;
}

.stat-card {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  text-align: center;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
  transition: transform 0.2s;
}

.stat-card:active {
  transform: scale(0.98);
}

.stat-icon {
  font-size: 48rpx;
  margin-bottom: 16rpx;
}

.stat-number {
  font-size: 48rpx;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 8rpx;
}

.stat-label {
  font-size: 28rpx;
  color: #6b7280;
  margin-bottom: 4rpx;
}

.stat-sublabel {
  font-size: 24rpx;
  color: #9ca3af;
}

/* 快捷操作 */
.quick-actions {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr 1fr;
  gap: 20rpx;
  padding: 20rpx 0;
}

.action-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx;
  border-radius: 12rpx;
  transition: background-color 0.2s;
}

.action-item:active {
  background-color: #f3f4f6;
}

.action-icon {
  font-size: 48rpx;
  margin-bottom: 12rpx;
}

.action-text {
  font-size: 24rpx;
  color: #6b7280;
  text-align: center;
}

/* 活动列表 */
.activity-list {
  padding: 20rpx 0;
}

.activity-item {
  padding: 24rpx 0;
  border-bottom: 1rpx solid #f3f4f6;
}

.activity-item:last-child {
  border-bottom: none;
}

.activity-content {
  display: flex;
  flex-direction: column;
}

.activity-title {
  font-size: 30rpx;
  font-weight: 500;
  color: #1f2937;
  margin-bottom: 8rpx;
}

.activity-desc {
  font-size: 26rpx;
  color: #6b7280;
  margin-bottom: 8rpx;
  line-height: 1.5;
}

.activity-time {
  font-size: 24rpx;
  color: #9ca3af;
}

/* 响应式设计 */
@media (max-width: 480px) {
  .stats-grid {
    grid-template-columns: 1fr;
  }
  
  .quick-actions {
    grid-template-columns: 1fr 1fr;
  }
  
  .user-name {
    font-size: 32rpx;
  }
  
  .stat-number {
    font-size: 42rpx;
  }
}
