<!--登录页面-->
<view class="login-container">
  <!-- 头部Logo -->
  <view class="header">
    <view class="logo">
      <view class="logo-placeholder">
        <text class="logo-text">🧪</text>
      </view>
    </view>
    <view class="title">LabSync</view>
    <view class="subtitle">实验室管理系统</view>
  </view>

  <!-- 登录表单 -->
  <view class="form-container">
    <view class="form-group">
      <view class="form-label">邮箱</view>
      <view class="input-wrapper">
        <input
          class="form-input"
          type="text"
          placeholder="请输入邮箱"
          value="{{email}}"
          bindinput="onEmailInput"
          maxlength="50"
        />
        <view class="input-icon">
          <text class="icon">📧</text>
        </view>
      </view>
    </view>

    <view class="form-group">
      <view class="form-label">密码</view>
      <view class="input-wrapper">
        <input
          class="form-input"
          type="{{showPassword ? 'text' : 'password'}}"
          placeholder="请输入密码"
          value="{{password}}"
          bindinput="onPasswordInput"
          maxlength="20"
        />
        <view class="input-icon" bindtap="togglePasswordVisibility">
          <text class="icon">{{showPassword ? '🙈' : '👁️'}}</text>
        </view>
      </view>
    </view>

    <!-- 记住我 -->
    <view class="form-options">
      <view class="checkbox-wrapper" bindtap="toggleRememberMe">
        <view class="checkbox {{rememberMe ? 'checked' : ''}}">
          <text class="checkmark" wx:if="{{rememberMe}}">✓</text>
        </view>
        <text class="checkbox-label">记住我</text>
      </view>
      <text class="forgot-password" bindtap="forgotPassword">忘记密码？</text>
    </view>

    <!-- 登录按钮 -->
    <button
      class="btn btn-primary btn-block login-btn"
      bindtap="handleLogin"
      loading="{{loading}}"
      disabled="{{loading}}"
    >
      {{loading ? '登录中...' : '登录'}}
    </button>

    <!-- 其他登录方式 -->
    <view class="divider">
      <view class="divider-line"></view>
      <text class="divider-text">或</text>
      <view class="divider-line"></view>
    </view>

    <button class="btn btn-outline btn-block wechat-btn" bindtap="wechatLogin">
      <text class="wechat-icon">💬</text>
      微信登录
    </button>

    <!-- 注册链接 -->
    <view class="register-link">
      <text>还没有账号？</text>
      <text class="link" bindtap="goToRegister">立即注册</text>
    </view>
  </view>
</view>
