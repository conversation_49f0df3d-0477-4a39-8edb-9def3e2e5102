# 图片资源说明

这个目录包含小程序所需的图片资源。

## 需要的图片文件

### TabBar 图标
- home.png - 首页图标
- home-active.png - 首页激活图标
- project.png - 项目图标
- project-active.png - 项目激活图标
- task.png - 任务图标
- task-active.png - 任务激活图标
- chat.png - 聊天图标
- chat-active.png - 聊天激活图标
- profile.png - 个人图标
- profile-active.png - 个人激活图标

### 其他图标
- logo.png - 应用Logo
- default-avatar.png - 默认头像

## 图片规格要求

### TabBar 图标
- 尺寸：81px × 81px
- 格式：PNG
- 背景：透明

### Logo
- 尺寸：120px × 120px
- 格式：PNG
- 背景：透明

### 头像
- 尺寸：120px × 120px
- 格式：PNG/JPG
- 背景：可选

## 临时解决方案

在开发阶段，可以使用以下方式：

1. **TabBar图标**: 可以暂时使用emoji或文字代替，在app.json中设置text属性
2. **Logo**: 可以使用在线图标生成器创建简单的Logo
3. **默认头像**: 可以使用纯色背景加文字的方式

## 注意事项

1. 所有图片都应该进行压缩优化
2. 图标应该使用简洁的设计风格
3. 颜色搭配应该与应用主题一致
4. 确保在不同设备上显示清晰

## 开发建议

在正式发布前，建议：
1. 设计专业的图标和Logo
2. 使用矢量图标确保清晰度
3. 遵循微信小程序设计规范
4. 进行多设备测试
