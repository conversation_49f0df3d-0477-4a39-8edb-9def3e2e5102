/* 全局样式 */
page {
  background-color: #f5f5f5;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Robot<PERSON>', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
  font-size: 28rpx;
  line-height: 1.6;
  color: #333;
}

/* 容器样式 */
.container {
  padding: 20rpx;
  min-height: 100vh;
}

.page-container {
  padding: 30rpx;
  background-color: #fff;
  min-height: calc(100vh - 60rpx);
}

/* 卡片样式 */
.card {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20rpx;
}

.card-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #1f2937;
}

.card-content {
  color: #6b7280;
  line-height: 1.6;
}

/* 按钮样式 */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 20rpx 40rpx;
  border-radius: 12rpx;
  font-size: 28rpx;
  font-weight: 500;
  text-align: center;
  transition: all 0.2s;
  border: none;
  cursor: pointer;
}

.btn-primary {
  background-color: #3b82f6;
  color: #fff;
}

.btn-primary:hover {
  background-color: #2563eb;
}

.btn-secondary {
  background-color: #6b7280;
  color: #fff;
}

.btn-success {
  background-color: #10b981;
  color: #fff;
}

.btn-warning {
  background-color: #f59e0b;
  color: #fff;
}

.btn-danger {
  background-color: #ef4444;
  color: #fff;
}

.btn-outline {
  background-color: transparent;
  border: 2rpx solid #d1d5db;
  color: #374151;
}

.btn-small {
  padding: 12rpx 24rpx;
  font-size: 24rpx;
}

.btn-large {
  padding: 28rpx 56rpx;
  font-size: 32rpx;
}

.btn-block {
  width: 100%;
  display: block;
}

/* 表单样式 */
.form-group {
  margin-bottom: 30rpx;
}

.form-label {
  display: block;
  margin-bottom: 12rpx;
  font-size: 28rpx;
  font-weight: 500;
  color: #374151;
}

.form-input {
  width: 100%;
  padding: 24rpx;
  border: 2rpx solid #d1d5db;
  border-radius: 12rpx;
  font-size: 28rpx;
  background-color: #fff;
  box-sizing: border-box;
}

.form-input:focus {
  border-color: #3b82f6;
  outline: none;
}

.form-textarea {
  min-height: 120rpx;
  resize: vertical;
}

/* 状态样式 */
.status {
  display: inline-flex;
  align-items: center;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  font-weight: 500;
}

.status-active {
  background-color: #dbeafe;
  color: #1d4ed8;
}

.status-completed {
  background-color: #d1fae5;
  color: #065f46;
}

.status-pending {
  background-color: #fef3c7;
  color: #92400e;
}

.status-cancelled {
  background-color: #fee2e2;
  color: #991b1b;
}

/* 优先级样式 */
.priority {
  display: inline-flex;
  align-items: center;
  padding: 6rpx 12rpx;
  border-radius: 16rpx;
  font-size: 22rpx;
  font-weight: 500;
}

.priority-low {
  background-color: #f3f4f6;
  color: #6b7280;
}

.priority-medium {
  background-color: #fef3c7;
  color: #d97706;
}

.priority-high {
  background-color: #fed7d7;
  color: #e53e3e;
}

.priority-urgent {
  background-color: #fecaca;
  color: #dc2626;
}

/* 列表样式 */
.list-item {
  display: flex;
  align-items: center;
  padding: 24rpx 0;
  border-bottom: 1rpx solid #f3f4f6;
}

.list-item:last-child {
  border-bottom: none;
}

.list-item-content {
  flex: 1;
  margin-left: 20rpx;
}

.list-item-title {
  font-size: 30rpx;
  font-weight: 500;
  color: #1f2937;
  margin-bottom: 8rpx;
}

.list-item-desc {
  font-size: 26rpx;
  color: #6b7280;
}

/* 头像样式 */
.avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  background-color: #e5e7eb;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.avatar-small {
  width: 60rpx;
  height: 60rpx;
}

.avatar-large {
  width: 120rpx;
  height: 120rpx;
}

.avatar image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* 徽章样式 */
.badge {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 36rpx;
  height: 36rpx;
  padding: 0 12rpx;
  background-color: #ef4444;
  color: #fff;
  font-size: 20rpx;
  font-weight: 500;
  border-radius: 18rpx;
  line-height: 1;
}

.badge-primary {
  background-color: #3b82f6;
}

.badge-success {
  background-color: #10b981;
}

.badge-warning {
  background-color: #f59e0b;
}

/* 工具类 */
.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.flex {
  display: flex;
}

.flex-1 {
  flex: 1;
}

.items-center {
  align-items: center;
}

.justify-between {
  justify-content: space-between;
}

.justify-center {
  justify-content: center;
}

.mb-20 {
  margin-bottom: 20rpx;
}

.mb-30 {
  margin-bottom: 30rpx;
}

.mt-20 {
  margin-top: 20rpx;
}

.mt-30 {
  margin-top: 30rpx;
}

.p-20 {
  padding: 20rpx;
}

.p-30 {
  padding: 30rpx;
}

/* 加载状态 */
.loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 60rpx;
  color: #6b7280;
}

/* 空状态 */
.empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 60rpx;
  color: #9ca3af;
  text-align: center;
}

.empty-icon {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 30rpx;
  opacity: 0.5;
}

.empty-text {
  font-size: 28rpx;
  margin-bottom: 20rpx;
}

.empty-desc {
  font-size: 24rpx;
  color: #d1d5db;
}

/* 动画 */
.fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
