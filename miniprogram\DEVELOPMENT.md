# LabSync 小程序开发指南

## 🚀 快速开始

### 1. 环境准备
1. 下载并安装 [微信开发者工具](https://developers.weixin.qq.com/miniprogram/dev/devtools/download.html)
2. 注册微信小程序账号（可使用测试号）

### 2. 项目导入
1. 打开微信开发者工具
2. 选择"导入项目"
3. 项目目录选择 `miniprogram` 文件夹
4. 填入 AppID（可使用测试号：`wx1234567890abcdef`）
5. 项目名称：LabSync

### 3. 配置修改
在 `app.js` 中修改API地址：
```javascript
globalData: {
  baseUrl: 'https://your-api-domain.com', // 替换为实际API地址
}
```

## 🔧 问题解决

### 常见错误及解决方案

#### 1. API连接错误
**错误信息**: `net::ERR_CONNECTION_REFUSED`
**解决方案**: 
- 当前使用模拟数据，无需后端API即可运行
- 如需连接真实API，请在 `app.js` 中配置正确的 `baseUrl`

#### 2. 图片资源缺失
**错误信息**: `Failed to load local image resource`
**解决方案**: 
- 已使用占位符代替图片资源
- TabBar 使用文字代替图标
- 头像使用文字占位符

#### 3. showLoading配对问题
**错误信息**: `showLoading 与 hideLoading 必须配对使用`
**解决方案**: 
- 已在 `utils/request.js` 中优化错误处理
- 确保每个 showLoading 都有对应的 hideLoading

## 📱 功能测试

### 登录功能
- 邮箱：任意有效邮箱格式
- 密码：至少6位字符
- 支持记住我功能

### 模拟数据
当API服务器未连接时，系统会自动使用模拟数据：
- 首页统计数据
- 项目列表数据
- 用户信息数据

## 🎨 界面适配

### 响应式设计
- 支持不同屏幕尺寸
- 自适应布局
- 统一的设计语言

### 主题色彩
- 主色：#3B82F6 (蓝色)
- 成功：#10B981 (绿色)
- 警告：#F59E0B (橙色)
- 错误：#EF4444 (红色)

## 🔄 开发流程

### 1. 页面开发
每个页面包含4个文件：
- `.js` - 页面逻辑
- `.wxml` - 页面结构
- `.wxss` - 页面样式
- `.json` - 页面配置

### 2. API集成
- 使用 `utils/api.js` 中定义的接口
- 支持自动错误处理
- 支持模拟数据后备

### 3. 样式开发
- 使用 `app.wxss` 中的全局样式
- 遵循统一的设计规范
- 支持响应式布局

## 📋 待办事项

### 高优先级
- [ ] 添加真实的图片资源
- [ ] 连接后端API服务
- [ ] 完善错误处理机制

### 中优先级
- [ ] 添加更多动画效果
- [ ] 优化性能
- [ ] 添加离线支持

### 低优先级
- [ ] 添加更多主题
- [ ] 支持国际化
- [ ] 添加无障碍支持

## 🐛 调试技巧

### 1. 控制台调试
- 使用 `console.log()` 输出调试信息
- 查看网络请求状态
- 监控错误信息

### 2. 真机调试
- 使用微信开发者工具的真机调试功能
- 测试不同设备的兼容性
- 验证网络请求

### 3. 性能优化
- 使用性能面板分析
- 优化图片资源
- 减少不必要的请求

## 📞 技术支持

如遇到问题，请检查：
1. 微信开发者工具版本是否最新
2. 基础库版本是否兼容
3. 网络连接是否正常
4. 代码语法是否正确

## 🎯 发布准备

### 发布前检查清单
- [ ] 所有功能测试通过
- [ ] 图片资源完整
- [ ] API地址配置正确
- [ ] 性能优化完成
- [ ] 真机测试通过

### 发布流程
1. 在微信开发者工具中点击"上传"
2. 填写版本号和项目备注
3. 在微信公众平台提交审核
4. 审核通过后发布

---

**Happy Coding! 🎉**
