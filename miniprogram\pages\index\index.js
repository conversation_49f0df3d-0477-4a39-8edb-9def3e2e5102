// 首页
const { dashboardApi } = require('../../utils/api.js');
const { formatRelativeTime } = require('../../utils/util.js');
const app = getApp();

Page({
  data: {
    userInfo: {},
    stats: {
      totalProjects: 0,
      activeProjects: 0,
      pendingTasks: 0,
      completedTasks: 0,
      unreadNotifications: 0,
      totalUsers: 0,
      unreadMessages: 0
    },
    recentActivities: [],
    loading: true
  },

  onLoad() {
    this.checkLogin();
  },

  onShow() {
    if (app.isLoggedIn()) {
      this.loadDashboardData();
    }
  },

  // 检查登录状态
  checkLogin() {
    if (!app.isLoggedIn()) {
      wx.reLaunch({
        url: '/pages/login/login'
      });
      return;
    }
    
    this.setData({
      userInfo: app.globalData.userInfo || {}
    });
    
    this.loadDashboardData();
  },

  // 加载仪表盘数据
  async loadDashboardData() {
    try {
      this.setData({ loading: true });

      const [statsRes, activitiesRes] = await Promise.all([
        dashboardApi.getStats(),
        dashboardApi.getRecentActivities()
      ]);

      this.setData({
        stats: statsRes.data || this.data.stats,
        recentActivities: activitiesRes.data || [],
        loading: false
      });

    } catch (error) {
      console.error('加载仪表盘数据失败:', error);
      // 使用模拟数据
      this.setData({
        stats: {
          totalProjects: 8,
          activeProjects: 3,
          pendingTasks: 12,
          completedTasks: 25,
          unreadNotifications: 5,
          totalUsers: 15,
          unreadMessages: 3
        },
        recentActivities: [
          {
            id: 1,
            title: '新任务分配',
            description: '您被分配了新任务：完成用户界面设计',
            createdAt: new Date(Date.now() - 2 * 60 * 60 * 1000)
          },
          {
            id: 2,
            title: '项目更新',
            description: 'LabSync项目进度更新至75%',
            createdAt: new Date(Date.now() - 5 * 60 * 60 * 1000)
          },
          {
            id: 3,
            title: '团队消息',
            description: '张三在群聊中发送了新消息',
            createdAt: new Date(Date.now() - 24 * 60 * 60 * 1000)
          }
        ],
        loading: false
      });
    }
  },

  // 下拉刷新
  onPullDownRefresh() {
    this.loadDashboardData();
    wx.stopPullDownRefresh();
  },

  // 跳转到项目页面
  goToProjects() {
    wx.switchTab({
      url: '/pages/projects/projects'
    });
  },

  // 跳转到任务页面
  goToTasks() {
    wx.switchTab({
      url: '/pages/tasks/tasks'
    });
  },

  // 跳转到聊天页面
  goToChat() {
    wx.switchTab({
      url: '/pages/chat/chat'
    });
  },

  // 跳转到团队页面
  goToTeam() {
    wx.navigateTo({
      url: '/pages/team/team'
    });
  },

  // 跳转到通知页面
  goToNotifications() {
    wx.navigateTo({
      url: '/pages/notifications/notifications'
    });
  },

  // 格式化时间
  formatTime(time) {
    return formatRelativeTime(time);
  }
});
