// 小程序主逻辑文件
App({
  globalData: {
    userInfo: null,
    token: null,
    baseUrl: 'http://localhost:3000', // 开发环境API地址，生产环境需要修改
    version: '1.0.0'
  },

  onLaunch() {
    console.log('LabSync小程序启动');
    
    // 检查登录状态
    this.checkLoginStatus();
    
    // 获取系统信息
    this.getSystemInfo();
  },

  onShow() {
    console.log('LabSync小程序显示');
  },

  onHide() {
    console.log('LabSync小程序隐藏');
  },

  onError(msg) {
    console.error('小程序错误:', msg);
  },

  // 检查登录状态
  checkLoginStatus() {
    const token = wx.getStorageSync('token');
    const userInfo = wx.getStorageSync('userInfo');
    
    if (token && userInfo) {
      this.globalData.token = token;
      this.globalData.userInfo = userInfo;
    }
  },

  // 获取系统信息
  getSystemInfo() {
    wx.getSystemInfo({
      success: (res) => {
        this.globalData.systemInfo = res;
        console.log('系统信息:', res);
      }
    });
  },

  // 设置用户信息
  setUserInfo(userInfo, token) {
    this.globalData.userInfo = userInfo;
    this.globalData.token = token;
    
    // 持久化存储
    wx.setStorageSync('userInfo', userInfo);
    wx.setStorageSync('token', token);
  },

  // 清除用户信息
  clearUserInfo() {
    this.globalData.userInfo = null;
    this.globalData.token = null;
    
    // 清除存储
    wx.removeStorageSync('userInfo');
    wx.removeStorageSync('token');
  },

  // 检查是否已登录
  isLoggedIn() {
    return !!(this.globalData.token && this.globalData.userInfo);
  }
});
