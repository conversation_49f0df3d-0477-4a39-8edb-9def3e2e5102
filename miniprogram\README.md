# LabSync 微信小程序版

基于原版 LabSync 实验室管理系统创建的微信小程序版本，保持相同的功能和界面逻辑，但针对移动端进行了优化。

## 🎯 功能特性

### ✅ 已实现的核心功能

#### 用户系统
- **登录页面** - 邮箱密码登录，支持记住我功能
- **注册页面** - 三步注册流程，包含邀请码验证
- **个人资料** - 用户信息展示和编辑

#### 项目管理
- **项目列表** - 支持搜索、筛选、分页加载
- **项目详情** - 项目信息、成员、任务管理
- **状态管理** - 计划中、进行中、已完成、已归档

#### 任务管理
- **任务列表** - 我的任务展示和管理
- **任务详情** - 任务信息、状态更新、分配管理
- **状态跟踪** - 待办、进行中、待审核、已完成

#### 聊天系统
- **聊天列表** - 群聊和私聊列表
- **聊天详情** - 实时消息、文件分享
- **未读提醒** - 消息数量提示

#### 文件管理
- **文件列表** - 项目文件管理
- **文件上传** - 支持多种文件格式
- **文件预览** - 在线预览功能

#### 通知系统
- **通知列表** - 系统通知和消息提醒
- **未读计数** - 实时未读数量显示

#### 团队管理
- **团队成员** - 成员列表和信息
- **邀请管理** - 邀请码生成和验证

## 🛠️ 技术架构

### 前端技术
- **微信小程序原生开发** - 最佳性能和兼容性
- **ES6+ JavaScript** - 现代化语法特性
- **WXML/WXSS** - 小程序专用标记语言
- **组件化开发** - 可复用的自定义组件

### 核心工具模块
- **网络请求封装** (`utils/request.js`) - 统一的HTTP请求处理
- **通用工具函数** (`utils/util.js`) - 时间格式化、验证等
- **API接口定义** (`utils/api.js`) - 完整的API接口封装

## 📁 项目结构

```
miniprogram/
├── app.js                 # 小程序主逻辑
├── app.json               # 小程序配置
├── app.wxss               # 全局样式
├── project.config.json    # 项目配置
├── sitemap.json           # 站点地图
├── pages/                 # 页面目录
│   ├── index/             # 首页
│   ├── login/             # 登录页
│   ├── register/          # 注册页
│   ├── projects/          # 项目列表
│   ├── project-detail/    # 项目详情
│   ├── tasks/             # 任务列表
│   ├── task-detail/       # 任务详情
│   ├── chat/              # 聊天列表
│   ├── chat-detail/       # 聊天详情
│   ├── files/             # 文件管理
│   ├── profile/           # 个人资料
│   ├── team/              # 团队管理
│   └── notifications/     # 通知中心
├── utils/                 # 工具函数
│   ├── api.js             # API接口
│   ├── request.js         # 网络请求
│   └── util.js            # 通用工具
└── images/                # 图片资源
```

## 🚀 快速开始

### 1. 环境准备
- 安装微信开发者工具
- 获取小程序 AppID（测试可使用测试号）

### 2. 项目配置
1. 打开微信开发者工具
2. 选择"导入项目"
3. 项目目录选择 `miniprogram` 文件夹
4. 填入 AppID 和项目名称

### 3. API 配置
在 `app.js` 中修改 API 地址：
```javascript
globalData: {
  baseUrl: 'https://your-api-domain.com', // 替换为实际API地址
}
```

### 4. 开始开发
- 点击"导入"按钮
- 项目将自动编译并在模拟器中显示

## 🎨 界面设计

### 设计原则
- **移动优先** - 专为移动端设计的交互体验
- **简洁明了** - 清晰的信息层次和导航结构
- **一致性** - 统一的设计语言和交互模式
- **响应式** - 适配不同屏幕尺寸的设备

### 主要特色
- **渐变背景** - 现代化的视觉效果
- **卡片布局** - 清晰的内容组织
- **状态指示** - 直观的状态显示
- **流畅动画** - 自然的页面切换效果

## 🔧 开发说明

### API 集成
- 所有 API 调用都通过 `utils/api.js` 统一管理
- 支持请求拦截、错误处理、加载状态
- 自动处理登录状态和 Token 管理

### 状态管理
- 使用小程序原生的数据绑定机制
- 全局状态通过 `app.js` 管理
- 本地存储用于持久化用户信息

### 错误处理
- 统一的错误提示机制
- 网络异常自动重试
- 用户友好的错误信息

## 📱 功能截图

*注：需要添加实际的功能截图*

## 🔄 与原版系统的对应关系

| 原版功能 | 小程序页面 | 实现状态 |
|---------|-----------|---------|
| 用户登录 | pages/login | ✅ 完成 |
| 用户注册 | pages/register | ✅ 完成 |
| 项目管理 | pages/projects | ✅ 完成 |
| 任务管理 | pages/tasks | ✅ 完成 |
| 聊天系统 | pages/chat | ✅ 完成 |
| 文件管理 | pages/files | ✅ 完成 |
| 个人中心 | pages/profile | ✅ 完成 |
| 团队管理 | pages/team | ✅ 完成 |
| 通知中心 | pages/notifications | ✅ 完成 |

## 🚧 待完善功能

1. **实时功能**
   - WebSocket 连接
   - 实时消息推送
   - 在线状态同步

2. **高级功能**
   - 离线缓存
   - 数据同步
   - 推送通知

3. **用户体验**
   - 更多动画效果
   - 手势操作
   - 无障碍支持

## 📄 许可证

与原版 LabSync 系统保持一致的许可证。

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！

---

**LabSync 微信小程序版 - 让实验室管理更便捷！** 📱✨
